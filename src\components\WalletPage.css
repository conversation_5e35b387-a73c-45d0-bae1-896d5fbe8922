.wallet-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
}

.wallet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px 30px;
  border-radius: 15px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.user-info h1 {
  color: #333;
  margin: 0 0 5px 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.user-info p {
  color: #666;
  margin: 0;
  font-size: 1rem;
}

.logout-button {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.logout-button:hover {
  background: #c0392b;
  transform: translateY(-2px);
}

.wallet-content {
  display: grid;
  gap: 30px;
  max-width: 800px;
  margin: 0 auto;
}

.balance-card {
  background: white;
  padding: 40px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.balance-header h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.5rem;
  font-weight: 600;
}

.balance-amount {
  font-size: 3rem;
  font-weight: 800;
  color: #27ae60;
  margin-bottom: 30px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.wallet-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.charge-button,
.cars-button {
  border: none;
  padding: 15px 30px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 160px;
}

.charge-button {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

.charge-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(39, 174, 96, 0.4);
}

.cars-button {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.cars-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(52, 152, 219, 0.4);
}

.transactions-section {
  background: white;
  padding: 30px;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.transactions-section h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.4rem;
  font-weight: 600;
}

.no-transactions {
  text-align: center;
  padding: 40px;
  color: #666;
}

.transactions-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #27ae60;
}

.transaction-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.transaction-type {
  font-weight: 600;
  color: #333;
}

.transaction-date {
  font-size: 0.9rem;
  color: #666;
}

.transaction-amount {
  font-weight: 700;
  font-size: 1.1rem;
}

.transaction-amount.charge {
  color: #27ae60;
}

.transaction-amount.payment {
  color: #e74c3c;
}

/* Cars Section Styles */
.cars-section {
  background: white;
  padding: 30px;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  margin-top: 30px;
}

.cars-section h3 {
  color: #333;
  margin-bottom: 25px;
  font-size: 1.4rem;
  font-weight: 600;
}

.no-cars {
  text-align: center;
  padding: 40px;
  color: #666;
}

.cars-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.car-card {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 20px;
  border-left: 4px solid #3498db;
  transition: all 0.3s ease;
}

.car-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.car-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e1e5e9;
}

.car-header h4 {
  color: #333;
  margin: 0;
  font-size: 1.2rem;
  font-weight: 700;
}

.car-year {
  background: #3498db;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.car-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.car-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  color: #666;
  font-weight: 500;
  font-size: 0.95rem;
}

.detail-value {
  color: #333;
  font-weight: 600;
  font-size: 0.95rem;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 20px;
  padding: 30px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.modal-header h3 {
  color: #333;
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: #f0f0f0;
  color: #333;
}

.charge-form .form-group {
  margin-bottom: 20px;
}

.charge-form label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 600;
}

.charge-form input {
  width: 100%;
  padding: 15px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-sizing: border-box;
  text-align: center;
}

.charge-form input:focus {
  outline: none;
  border-color: #27ae60;
  box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
}

.modal-actions {
  display: flex;
  gap: 15px;
  margin-top: 25px;
}

.confirm-button {
  flex: 1;
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  border: none;
  padding: 15px;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.confirm-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

.confirm-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.cancel-button {
  flex: 1;
  background: #6c757d;
  color: white;
  border: none;
  padding: 15px;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-button:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-2px);
}

.error-message {
  background: #fee;
  color: #e74c3c;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 15px;
  font-weight: 500;
  border: 1px solid #fcc;
  text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .wallet-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .balance-card {
    padding: 30px 20px;
  }

  .balance-amount {
    font-size: 2.5rem;
  }

  .transaction-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .modal-actions {
    flex-direction: column;
  }

  .wallet-actions {
    flex-direction: column;
    align-items: center;
  }

  .cars-grid {
    grid-template-columns: 1fr;
  }

  .car-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}