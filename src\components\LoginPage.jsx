import { useState } from 'react'
import './LoginPage.css'

const LoginPage = ({ onLogin }) => {
  const [nationalId, setNationalId] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  // Validate Egyptian National ID (14 digits)
  const validateNationalId = (id) => {
    return /^\d{14}$/.test(id)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')
    setIsLoading(true)

    // Validate inputs
    if (!nationalId || !password) {
      setError('يرجى إدخال الرقم القومي وكلمة المرور')
      setIsLoading(false)
      return
    }

    if (!validateNationalId(nationalId)) {
      setError('الرقم القومي يجب أن يكون 14 رقم')
      setIsLoading(false)
      return
    }

    if (password.length < 6) {
      setError('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
      setIsLoading(false)
      return
    }

    // Simulate login process
    setTimeout(() => {
      // Check if user exists in localStorage
      const users = JSON.parse(localStorage.getItem('walletUsers') || '{}')
      
      if (users[nationalId] && users[nationalId].password === password) {
        // User exists and password is correct
        onLogin({
          nationalId,
          name: users[nationalId].name,
          balance: users[nationalId].balance
        })
      } else if (!users[nationalId]) {
        // New user - create account
        const newUser = {
          password,
          name: `مستخدم ${nationalId.slice(-4)}`, // Use last 4 digits as name
          balance: 0,
          createdAt: new Date().toISOString()
        }
        
        users[nationalId] = newUser
        localStorage.setItem('walletUsers', JSON.stringify(users))
        
        onLogin({
          nationalId,
          name: newUser.name,
          balance: newUser.balance
        })
      } else {
        // Wrong password
        setError('كلمة المرور غير صحيحة')
      }
      
      setIsLoading(false)
    }, 1000)
  }

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <h1>💳 المحفظة الإلكترونية</h1>
          <p>سجل دخولك للوصول إلى محفظتك</p>
        </div>

        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <label htmlFor="nationalId">الرقم القومي</label>
            <input
              type="text"
              id="nationalId"
              value={nationalId}
              onChange={(e) => setNationalId(e.target.value)}
              placeholder="أدخل الرقم القومي (14 رقم)"
              maxLength="14"
              className={error && !validateNationalId(nationalId) ? 'error' : ''}
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">كلمة المرور</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="أدخل كلمة المرور"
              className={error && password.length < 6 ? 'error' : ''}
            />
          </div>

          {error && <div className="error-message">{error}</div>}

          <button 
            type="submit" 
            className="login-button"
            disabled={isLoading}
          >
            {isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
          </button>
        </form>

        <div className="login-info">
          <p>💡 إذا كنت مستخدم جديد، سيتم إنشاء حساب تلقائياً</p>
        </div>
      </div>
    </div>
  )
}

export default LoginPage
