import { useState, useEffect } from 'react'
import './WalletPage.css'
import { updateUserBalance } from '../data/database'

const WalletPage = ({ user, onLogout }) => {
  const [balance, setBalance] = useState(user.balance)
  const [showChargeModal, setShowChargeModal] = useState(false)
  const [chargeAmount, setChargeAmount] = useState('')
  const [isCharging, setIsCharging] = useState(false)
  const [chargeError, setChargeError] = useState('')
  const [transactions, setTransactions] = useState([])
  const [showCars, setShowCars] = useState(false)
  const [userCars, setUserCars] = useState([])

  // Load transactions and cars on component mount
  useEffect(() => {
    const userTransactions = JSON.parse(localStorage.getItem(`transactions_${user.nationalId}`) || '[]')
    setTransactions(userTransactions)

    // Load user cars
    if (user.cars) {
      setUserCars(user.cars)
    }
  }, [user.nationalId, user.cars])

  const handleCharge = async (e) => {
    e.preventDefault()
    setChargeError('')
    setIsCharging(true)

    // Validate charge amount
    const amount = parseFloat(chargeAmount)
    if (!amount || amount <= 0) {
      setChargeError('يرجى إدخال مبلغ صحيح')
      setIsCharging(false)
      return
    }

    if (amount > 10000) {
      setChargeError('الحد الأقصى للشحن هو 10,000 جنيه')
      setIsCharging(false)
      return
    }

    // Simulate charging process
    setTimeout(() => {
      const newBalance = balance + amount
      setBalance(newBalance)

      // Update user balance in database
      updateUserBalance(user.nationalId, newBalance)

      // Add transaction record
      const newTransaction = {
        id: Date.now(),
        type: 'charge',
        amount: amount,
        date: new Date().toISOString(),
        description: 'شحن المحفظة'
      }

      const updatedTransactions = [newTransaction, ...transactions]
      setTransactions(updatedTransactions)
      localStorage.setItem(`transactions_${user.nationalId}`, JSON.stringify(updatedTransactions))

      // Reset form
      setChargeAmount('')
      setShowChargeModal(false)
      setIsCharging(false)
    }, 1500)
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="wallet-container">
      <div className="wallet-header">
        <div className="user-info">
          <h1>مرحباً، {user.name}</h1>
          <p>الرقم القومي: {user.nationalId}</p>
        </div>
        <button onClick={onLogout} className="logout-button">
          تسجيل الخروج
        </button>
      </div>

      <div className="wallet-content">
        <div className="balance-card">
          <div className="balance-header">
            <h2>💰 رصيد المحفظة</h2>
          </div>
          <div className="balance-amount">
            {formatCurrency(balance)}
          </div>
          <div className="wallet-actions">
            <button
              onClick={() => setShowChargeModal(true)}
              className="charge-button"
            >
              ⚡ شحن المحفظة
            </button>
            <button
              onClick={() => setShowCars(!showCars)}
              className="cars-button"
            >
              🚗 {showCars ? 'إخفاء السيارات' : 'عرض السيارات'}
            </button>
          </div>
        </div>

        <div className="transactions-section">
          <h3>📋 آخر العمليات</h3>
          {transactions.length === 0 ? (
            <div className="no-transactions">
              <p>لا توجد عمليات حتى الآن</p>
            </div>
          ) : (
            <div className="transactions-list">
              {transactions.slice(0, 10).map((transaction) => (
                <div key={transaction.id} className="transaction-item">
                  <div className="transaction-info">
                    <span className="transaction-type">
                      {transaction.type === 'charge' ? '⚡' : '💸'} {transaction.description}
                    </span>
                    <span className="transaction-date">
                      {formatDate(transaction.date)}
                    </span>
                  </div>
                  <div className={`transaction-amount ${transaction.type}`}>
                    {transaction.type === 'charge' ? '+' : '-'}{formatCurrency(transaction.amount)}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Cars Section */}
        {showCars && (
          <div className="cars-section">
            <h3>🚗 سياراتي</h3>
            {userCars.length === 0 ? (
              <div className="no-cars">
                <p>لا توجد سيارات مسجلة</p>
              </div>
            ) : (
              <div className="cars-grid">
                {userCars.map((car) => (
                  <div key={car.id} className="car-card">
                    <div className="car-header">
                      <h4>{car.brand} {car.model}</h4>
                      <span className="car-year">{car.year}</span>
                    </div>
                    <div className="car-details">
                      <div className="car-detail">
                        <span className="detail-label">اللون:</span>
                        <span className="detail-value">{car.color}</span>
                      </div>
                      <div className="car-detail">
                        <span className="detail-label">رقم اللوحة:</span>
                        <span className="detail-value">{car.plateNumber}</span>
                      </div>
                      <div className="car-detail">
                        <span className="detail-label">حجم المحرك:</span>
                        <span className="detail-value">{car.engineSize}</span>
                      </div>
                      <div className="car-detail">
                        <span className="detail-label">نوع الوقود:</span>
                        <span className="detail-value">{car.fuelType}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Charge Modal */}
      {showChargeModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>⚡ شحن المحفظة</h3>
              <button 
                onClick={() => setShowChargeModal(false)}
                className="close-button"
              >
                ✕
              </button>
            </div>

            <form onSubmit={handleCharge} className="charge-form">
              <div className="form-group">
                <label htmlFor="chargeAmount">المبلغ (بالجنيه المصري)</label>
                <input
                  type="number"
                  id="chargeAmount"
                  value={chargeAmount}
                  onChange={(e) => setChargeAmount(e.target.value)}
                  placeholder="أدخل المبلغ"
                  min="1"
                  max="10000"
                  step="0.01"
                  required
                />
              </div>

              {chargeError && <div className="error-message">{chargeError}</div>}

              <div className="modal-actions">
                <button 
                  type="submit" 
                  className="confirm-button"
                  disabled={isCharging}
                >
                  {isCharging ? 'جاري الشحن...' : 'تأكيد الشحن'}
                </button>
                <button 
                  type="button" 
                  onClick={() => setShowChargeModal(false)}
                  className="cancel-button"
                  disabled={isCharging}
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default WalletPage
