import { useState, useEffect } from 'react'
import './WalletPage.css'

const WalletPage = ({ user, onLogout }) => {
  const [balance, setBalance] = useState(user.balance)
  const [showChargeModal, setShowChargeModal] = useState(false)
  const [chargeAmount, setChargeAmount] = useState('')
  const [isCharging, setIsCharging] = useState(false)
  const [chargeError, setChargeError] = useState('')
  const [transactions, setTransactions] = useState([])

  // Load transactions on component mount
  useEffect(() => {
    const userTransactions = JSON.parse(localStorage.getItem(`transactions_${user.nationalId}`) || '[]')
    setTransactions(userTransactions)
  }, [user.nationalId])

  const handleCharge = async (e) => {
    e.preventDefault()
    setChargeError('')
    setIsCharging(true)

    // Validate charge amount
    const amount = parseFloat(chargeAmount)
    if (!amount || amount <= 0) {
      setChargeError('يرجى إدخال مبلغ صحيح')
      setIsCharging(false)
      return
    }

    if (amount > 10000) {
      setChargeError('الحد الأقصى للشحن هو 10,000 جنيه')
      setIsCharging(false)
      return
    }

    // Simulate charging process
    setTimeout(() => {
      const newBalance = balance + amount
      setBalance(newBalance)

      // Update user data in localStorage
      const users = JSON.parse(localStorage.getItem('walletUsers') || '{}')
      if (users[user.nationalId]) {
        users[user.nationalId].balance = newBalance
        localStorage.setItem('walletUsers', JSON.stringify(users))
      }

      // Add transaction record
      const newTransaction = {
        id: Date.now(),
        type: 'charge',
        amount: amount,
        date: new Date().toISOString(),
        description: 'شحن المحفظة'
      }

      const updatedTransactions = [newTransaction, ...transactions]
      setTransactions(updatedTransactions)
      localStorage.setItem(`transactions_${user.nationalId}`, JSON.stringify(updatedTransactions))

      // Reset form
      setChargeAmount('')
      setShowChargeModal(false)
      setIsCharging(false)
    }, 1500)
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="wallet-container">
      <div className="wallet-header">
        <div className="user-info">
          <h1>مرحباً، {user.name}</h1>
          <p>الرقم القومي: {user.nationalId}</p>
        </div>
        <button onClick={onLogout} className="logout-button">
          تسجيل الخروج
        </button>
      </div>

      <div className="wallet-content">
        <div className="balance-card">
          <div className="balance-header">
            <h2>💰 رصيد المحفظة</h2>
          </div>
          <div className="balance-amount">
            {formatCurrency(balance)}
          </div>
          <button 
            onClick={() => setShowChargeModal(true)}
            className="charge-button"
          >
            ⚡ شحن المحفظة
          </button>
        </div>

        <div className="transactions-section">
          <h3>📋 آخر العمليات</h3>
          {transactions.length === 0 ? (
            <div className="no-transactions">
              <p>لا توجد عمليات حتى الآن</p>
            </div>
          ) : (
            <div className="transactions-list">
              {transactions.slice(0, 10).map((transaction) => (
                <div key={transaction.id} className="transaction-item">
                  <div className="transaction-info">
                    <span className="transaction-type">
                      {transaction.type === 'charge' ? '⚡' : '💸'} {transaction.description}
                    </span>
                    <span className="transaction-date">
                      {formatDate(transaction.date)}
                    </span>
                  </div>
                  <div className={`transaction-amount ${transaction.type}`}>
                    {transaction.type === 'charge' ? '+' : '-'}{formatCurrency(transaction.amount)}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Charge Modal */}
      {showChargeModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>⚡ شحن المحفظة</h3>
              <button 
                onClick={() => setShowChargeModal(false)}
                className="close-button"
              >
                ✕
              </button>
            </div>

            <form onSubmit={handleCharge} className="charge-form">
              <div className="form-group">
                <label htmlFor="chargeAmount">المبلغ (بالجنيه المصري)</label>
                <input
                  type="number"
                  id="chargeAmount"
                  value={chargeAmount}
                  onChange={(e) => setChargeAmount(e.target.value)}
                  placeholder="أدخل المبلغ"
                  min="1"
                  max="10000"
                  step="0.01"
                  required
                />
              </div>

              {chargeError && <div className="error-message">{chargeError}</div>}

              <div className="modal-actions">
                <button 
                  type="submit" 
                  className="confirm-button"
                  disabled={isCharging}
                >
                  {isCharging ? 'جاري الشحن...' : 'تأكيد الشحن'}
                </button>
                <button 
                  type="button" 
                  onClick={() => setShowChargeModal(false)}
                  className="cancel-button"
                  disabled={isCharging}
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default WalletPage
